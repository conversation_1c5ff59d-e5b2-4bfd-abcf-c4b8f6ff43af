package config

import (
	"log"
	"time"

	"github.com/caarlos0/env/v11"
)

type Configs struct {
	App     *App
	MongoDB *Mongodb
}

func New() *Configs {
	cfg := &Configs{
		App:     &App{},
		MongoDB: &Mongodb{},
	}
	if err := env.Parse(cfg); err != nil {
		log.Fatalf("Failed to load configs: %v", err)
	}

	return cfg
}

type Mongodb struct {
	Database   string `env:"MONGO_DATABASE" envDefault:"common"`
	Collection string `env:"MONGO_COLLECTION" envDefault:"opt-evses"`
}

type App struct {
	LogLevel    string        `env:"LOG_LEVEL" envDefault:"debug"`
	ServerPort  int           `env:"SERVER_PORT" envDefault:"8080"`
	ReadTimeout time.Duration `env:"SERVER_READ_TIMEOUT" envDefault:"30s"`
}
