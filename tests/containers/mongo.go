package containers

import (
	"context"

	"github.com/rs/zerolog/log"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoContainer struct {
	mongoClient      *mongo.Client
	mongoURI         string
	mongodbContainer *mongodb.MongoDBContainer
}

func NewMongoContainer() *MongoContainer {
	return &MongoContainer{}
}
func (m *MongoContainer) RunContainer() error {
	ctx := context.Background()
	mongodbContainer, err := mongodb.Run(ctx, "mongo:latest")
	if err != nil {
		log.Printf("failed to start container: %s", err)
		return err
	}
	endpoint, err := mongodbContainer.ConnectionString(ctx)
	if err != nil {
		log.Printf("failed to get connection string: %s", err)
		return err
	}
	mongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(endpoint))
	if err != nil {
		log.Printf("failed to connect to MongoDB: %s", err)
		return err
	}
	m.mongoURI = endpoint
	m.mongoClient = mongoClient
	m.mongodbContainer = mongodbContainer
	return nil
}

func (m *MongoContainer) GetClient() *mongo.Client {
	return m.mongoClient
}
func (m *MongoContainer) GetConnectionString() string {
	return m.mongoURI
}
func (m *MongoContainer) DeleteContainer() {
	if m.mongodbContainer == nil {
		return
	}
	if err := testcontainers.TerminateContainer(m.mongodbContainer); err != nil {
		log.Printf("failed to terminate container: %s", err)
	}
}
