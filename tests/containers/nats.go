package containers

import (
	"context"
	"fmt"

	"github.com/nats-io/nats.go"
	"github.com/nats-io/nats.go/jetstream"
	natstc "github.com/testcontainers/testcontainers-go/modules/nats"
)

type NATSContainer struct {
	container *natstc.NATSContainer
	js        jetstream.JetStream
}

func NewNatsContainer() *NATSContainer {
	return &NATSContainer{}
}

func (p *NATSContainer) InitContainer() error {
	ctx := context.Background()
	container, err := natstc.Run(ctx, "nats:latest")
	if err != nil {
		return err
	}
	p.container = container

	url := container.MustConnectionString(ctx)
	fmt.Printf("NATS server URL: %s\n", url)

	opts := nats.GetDefaultOptions()
	opts.Url = url
	opts.Pedantic = true
	opts.RetryOnFailedConnect = true

	nc, err := opts.Connect()
	if err != nil {
		return err
	}

	js, err := jetstream.New(nc)
	if err != nil {
		return err
	}
	p.js = js

	return nil
}

func (p *NATSContainer) DestroyContainer() error {
	return p.container.Terminate(context.Background())
}

func (p *NATSContainer) ServerURL() string {
	return p.container.MustConnectionString(context.Background())
}

func (p *NATSContainer) CreateStream(name, subject string) (jetstream.Stream, error) {
	return p.js.CreateStream(context.Background(), jetstream.StreamConfig{
		Name:     name,
		Subjects: []string{subject}})
}

func (p *NATSContainer) StreamInfo(name string) (*jetstream.StreamInfo, error) {
	stream, err := p.js.Stream(context.Background(), name)
	if err != nil {
		return nil, err
	}
	return stream.Info(context.Background())
}

func (p *NATSContainer) PurgeStream(name string) error {
	stream, err := p.js.Stream(context.Background(), name)
	if err != nil {
		return err
	}
	return stream.Purge(context.Background())
}

func (p *NATSContainer) DeleteStream(name string) error {
	return p.js.DeleteStream(context.Background(), name)
}
