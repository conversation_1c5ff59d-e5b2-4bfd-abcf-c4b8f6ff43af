package port

import (
	"context"

	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

type EvseRepository interface {
	Get(ctx context.Context, evseID string) (*openapi.EvseData, error)
	GetEvsesByDeviceID(ctx context.Context, deviceID string) ([]openapi.EvseData, error)
	Upsert(ctx context.Context, input *openapi.EvseData) error
}

type EvseService interface {
	Get(ctx context.Context, evseID string) (*openapi.EvseData, error)
	GetEvsesByDeviceID(ctx context.Context, deviceID string) ([]openapi.EvseData, error)
	Save(ctx context.Context, input *openapi.EvseData) (*openapi.EvseData, error)
}
