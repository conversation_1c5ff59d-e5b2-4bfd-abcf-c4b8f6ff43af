package service

import (
	"context"

	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/port"
	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

var _ port.EvseService = (*EvseService)(nil)

type EvseService struct {
	evseRepo port.EvseRepository
}

// GetEvsesByDeviceID implements port.EvseService.
func (s *EvseService) GetEvsesByDeviceID(ctx context.Context, deviceID string) ([]openapi.EvseData, error) {
	return s.evseRepo.GetEvsesByDeviceID(ctx, deviceID)
}

// Get implements port.EvseRepository.
func (s *EvseService) Get(ctx context.Context, evseID string) (*openapi.EvseData, error) {
	return s.evseRepo.Get(ctx, evseID)
}

// Save implements port.EvseRepository.
func (s *EvseService) Save(ctx context.Context, input *openapi.EvseData) (*openapi.EvseData, error) {
	err := s.evseRepo.Upsert(ctx, input)
	if err != nil {
		return nil, err
	}
	return s.Get(ctx, input.EvseUID)
}

func NewEvseService(evseRepo port.EvseRepository) *EvseService {
	return &EvseService{evseRepo: evseRepo}
}
