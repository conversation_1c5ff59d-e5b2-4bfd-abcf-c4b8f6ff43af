package resthttp

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"inv-cloud-platform/fe-ev-opt-evse-api/config"
)

const serverShutdownTimeoutDuration = 60 * time.Second

type App struct {
	cfg    *config.App
	Server *http.Server
}

func NewHTTPServer(
	cfg *config.App,
	handler http.Handler,
	middlewares ...func(http.Handler) http.Handler,
) *App {
	r := chi.NewRouter()

	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(middleware.Recoverer)
	r.Use(middleware.CleanPath)
	r.Use(middleware.Heartbeat("/health"))

	r.Mount("/metrics", promhttp.Handler())
	r.With(middlewares...).Mount("/", handler)

	server := http.Server{
		Handler:     r,
		Addr:        fmt.Sprintf(":%d", cfg.ServerPort),
		ReadTimeout: cfg.ReadTimeout,
	}

	return &App{
		cfg:    cfg,
		Server: &server,
	}
}

func (s *App) Start() {
	log.Info().Msgf("Starting HTTP sever on port %d", s.cfg.ServerPort)
	err := s.Server.ListenAndServe()
	if errors.Is(err, http.ErrServerClosed) {
		log.Printf("HTTP server closed")
	} else if err != nil {
		log.Fatal().Msgf("Failed to start HTTP server: %v", err)
	}
}

func (s *App) Shutdown(ctx context.Context) error {
	log.Info().Msg("Shutting down HTTP server")
	if s.Server != nil {
		shutdownCtx, shutdownRelease := context.WithTimeout(ctx, serverShutdownTimeoutDuration)
		err := s.Server.Shutdown(shutdownCtx)
		shutdownRelease()
		return err
	}
	return nil
}
