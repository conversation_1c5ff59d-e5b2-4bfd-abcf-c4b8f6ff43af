package resthttp

import (
	"context"
	"encoding/json"

	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/service"
	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

// Check if operation satisfies interface.
var _ openapi.StrictServerInterface = (*EvseController)(nil)

type EvseController struct {
	evseSvc *service.EvseService
}

// GetEvses implements openapi.StrictServerInterface.
func (c *EvseController) GetEvses(ctx context.Context, request openapi.GetEvsesRequestObject) (openapi.GetEvsesResponseObject, error) {
	res, err := c.evseSvc.GetEvsesByDeviceID(ctx, request.DeviceID)
	if err != nil {
		return openapi.GetEvses400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: err.<PERSON>rror(),
			},
		}, nil
	}
	return openapi.GetEvses200JSONResponse{
		Data:   res,
		Status: true,
	}, nil
}

// GetEvse implements openapi.StrictServerInterface.
func (c *EvseController) GetEvse(ctx context.Context, request openapi.GetEvseRequestObject) (openapi.GetEvseResponseObject, error) {
	if request.Params.EvseUid == nil {
		return openapi.GetEvse400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: domain.EvseIDIsBlank,
			},
		}, nil
	}
	res, err := c.evseSvc.Get(ctx, *request.Params.EvseUid)
	if err != nil {
		return openapi.GetEvse204JSONResponse{
			N204JSONResponse: openapi.N204JSONResponse{
				Code:    "204",
				Message: err.Error(),
			},
		}, nil
	}
	return openapi.GetEvse200JSONResponse{
		ChargerID:   res.ChargerID,
		Connection:  res.Connection,
		Environment: res.Environment,
		EvseUID:     res.EvseUID,
		Site:        res.Site,
		UccID:       res.UccID,
	}, nil
}

// CreateEvse implements openapi.StrictServerInterface.
func (c *EvseController) CreateEvse(ctx context.Context, request openapi.CreateEvseRequestObject) (openapi.CreateEvseResponseObject, error) {
	res, err := c.evseSvc.Save(ctx, request.Body)
	if err != nil {
		return openapi.CreateEvse400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: err.Error(),
			},
		}, nil
	}
	return openapi.CreateEvse200JSONResponse{
		ChargerID:   res.ChargerID,
		Connection:  res.Connection,
		Environment: res.Environment,
		EvseUID:     res.EvseUID,
		Site:        res.Site,
		UccID:       res.UccID,
	}, nil
}

// GetAllEvses implements openapi.StrictServerInterface.
func (c *EvseController) GetAllEvses(ctx context.Context, request openapi.GetAllEvsesRequestObject) (openapi.GetAllEvsesResponseObject, error) {
	// Convert query parameters to SearchOptions
	searchOptions := domain.SearchOptions{}

	if request.Params.Filter != nil {
		searchOptions.Filter = *request.Params.Filter
	}

	if request.Params.Offset != nil {
		searchOptions.Offset = *request.Params.Offset
	} else {
		searchOptions.Offset = 0
	}

	if request.Params.Limit != nil {
		searchOptions.Limit = *request.Params.Limit
	} else {
		searchOptions.Limit = 50 // Default limit
	}

	// Parse sort parameter if provided (JSON format)
	if request.Params.Sort != nil {
		var sortMap map[string]int
		if err := json.Unmarshal([]byte(*request.Params.Sort), &sortMap); err != nil {
			return openapi.GetAllEvses400JSONResponse{
				N400JSONResponse: openapi.N400JSONResponse{
					Code:    "400",
					Message: "Invalid sort parameter format: " + err.Error(),
				},
			}, nil
		}
		searchOptions.Sort = sortMap
	}

	// Parse projection parameter if provided (JSON format)
	if request.Params.Projection != nil {
		var projectionMap map[string]int
		if err := json.Unmarshal([]byte(*request.Params.Projection), &projectionMap); err != nil {
			return openapi.GetAllEvses400JSONResponse{
				N400JSONResponse: openapi.N400JSONResponse{
					Code:    "400",
					Message: "Invalid projection parameter format: " + err.Error(),
				},
			}, nil
		}
		searchOptions.Projection = projectionMap
	}

	// Call the service
	result, err := c.evseSvc.FindAll(ctx, searchOptions)
	if err != nil {
		return openapi.GetAllEvses400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: err.Error(),
			},
		}, nil
	}

	return openapi.GetAllEvses200JSONResponse{
		Data:   result.Data,
		Status: true,
		Total:  result.Total,
	}, nil
}

func NewEvseController(evseSvc *service.EvseService) *EvseController {
	return &EvseController{evseSvc: evseSvc}
}
