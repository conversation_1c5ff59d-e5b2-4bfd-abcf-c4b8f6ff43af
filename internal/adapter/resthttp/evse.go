package resthttp

import (
	"context"

	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/service"
	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

// Check if operation satisfies interface.
var _ openapi.StrictServerInterface = (*EvseController)(nil)

type EvseController struct {
	evseSvc *service.EvseService
}

// GetEvses implements openapi.StrictServerInterface.
func (c *EvseController) GetEvses(ctx context.Context, request openapi.GetEvsesRequestObject) (openapi.GetEvsesResponseObject, error) {
	res, err := c.evseSvc.GetEvsesByDeviceID(ctx, request.DeviceID)
	if err != nil {
		return openapi.GetEvses400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: err.<PERSON><PERSON>r(),
			},
		}, nil
	}
	return openapi.GetEvses200JSONResponse{
		Data:   res,
		Status: true,
	}, nil
}

// GetEvse implements openapi.StrictServerInterface.
func (c *EvseController) GetEvse(ctx context.Context, request openapi.GetEvseRequestObject) (openapi.GetEvseResponseObject, error) {
	if request.Params.EvseUid == nil {
		return openapi.GetEvse400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: domain.EvseIDIsBlank,
			},
		}, nil
	}
	res, err := c.evseSvc.Get(ctx, *request.Params.EvseUid)
	if err != nil {
		return openapi.GetEvse204JSONResponse{
			N204JSONResponse: openapi.N204JSONResponse{
				Code:    "204",
				Message: err.Error(),
			},
		}, nil
	}
	return openapi.GetEvse200JSONResponse{
		ChargerID:   res.ChargerID,
		Connection:  res.Connection,
		Environment: res.Environment,
		EvseUID:     res.EvseUID,
		Site:        res.Site,
		UccID:       res.UccID,
	}, nil
}

// CreateEvse implements openapi.StrictServerInterface.
func (c *EvseController) CreateEvse(ctx context.Context, request openapi.CreateEvseRequestObject) (openapi.CreateEvseResponseObject, error) {
	res, err := c.evseSvc.Save(ctx, request.Body)
	if err != nil {
		return openapi.CreateEvse400JSONResponse{
			N400JSONResponse: openapi.N400JSONResponse{
				Code:    "400",
				Message: err.Error(),
			},
		}, nil
	}
	return openapi.CreateEvse200JSONResponse{
		ChargerID:   res.ChargerID,
		Connection:  res.Connection,
		Environment: res.Environment,
		EvseUID:     res.EvseUID,
		Site:        res.Site,
		UccID:       res.UccID,
	}, nil
}

func NewEvseController(evseSvc *service.EvseService) *EvseController {
	return &EvseController{evseSvc: evseSvc}
}
