package mongodb

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/mongo"
	"inv-cloud-platform/fe-ev-opt-evse-api/config"
	"inv-cloud-platform/fe-ev-opt-evse-api/tests/containers"
)

type RepositorySuite struct {
	suite.Suite
	ctx           context.Context
	cfg           *config.Configs
	mongoClient   *mongo.Client
	mongoDatabase *mongo.Database
	repo          *EvseRepo
}

func (suite *RepositorySuite) SetupSuite() {
	suite.cfg = config.New()
	suite.ctx = context.Background()
	// setup all the tests containers
	suite.setupContainers()
	suite.repo = NewEvseRepo(suite.cfg, suite.mongoClient)
}

func (suite *RepositorySuite) setupContainers() {
	mongoContainer := containers.NewMongoContainer()
	err := mongoContainer.RunContainer()
	suite.Require().Nil(err)
	suite.cfg.MongoDB.Collection = "tariffs"
	suite.mongoClient = mongoContainer.GetClient()
	suite.mongoDatabase = suite.mongoClient.Database(suite.cfg.MongoDB.Database)
}

func (suite *RepositorySuite) TearDownSuite() {
	errDisconnect := suite.mongoClient.Disconnect(suite.ctx)
	suite.Require().NoError(errDisconnect)
}

func (suite *RepositorySuite) getEvseData(evseUID string) *EvseData {
	return &EvseData{
		ChargerID: "chargerid",
		Connection: Connection{
			CountryCode: "us",
			PartyID:     "dom",
		},
		Environment: "dev",
		EvseUID:     evseUID,
		Site:        "GVR",
		UccID:       "device-test",
	}
}

func (suite *RepositorySuite) TestTariff() {
	id := "evse-1"
	data := suite.getEvseData(id)
	suite.Run("add new record success", func() {
		err := suite.repo.Upsert(suite.ctx, data.ToJSON())
		suite.Require().NoError(err)
		resp, errGet := suite.repo.Get(suite.ctx, id)
		suite.Require().NoError(errGet)
		suite.Require().NotNil(resp)
		suite.Equal(resp.EvseUID, id)
	})

	suite.Run("update record success", func() {
		data.ChargerID = "updated_charger_123"
		err := suite.repo.Upsert(suite.ctx, data.ToJSON())
		suite.Require().NoError(err)
		resp, errGet := suite.repo.Get(suite.ctx, id)
		suite.Require().NoError(errGet)
		suite.Require().NotNil(resp)

		suite.Equal(resp.ChargerID, data.ChargerID)
	})
}
func TestRepositorySuite(t *testing.T) {
	suite.Run(t, new(RepositorySuite))
}
