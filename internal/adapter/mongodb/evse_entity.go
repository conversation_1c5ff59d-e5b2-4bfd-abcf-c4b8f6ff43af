package mongodb

import "inv-cloud-platform/fe-ev-opt-evse-api/openapi"

type EvseData struct {
	ChargerID   string     `bson:"charger_id"`
	Connection  Connection `bson:"connection"`
	Environment string     `bson:"environment"`
	EvseUID     string     `bson:"evse_uid"`
	Site        string     `bson:"site"`
	UccID       string     `bson:"ucc_id"`
}

type Connection struct {
	CountryCode string `bson:"country_code,omitempty"`
	PartyID     string `bson:"party_id,omitempty"`
}

func (e *EvseData) ToJSON() *openapi.EvseData {
	return &openapi.EvseData{
		ChargerID: e.ChargerID,
		Connection: openapi.Connection{
			CountryCode: e.Connection.CountryCode,
			PartyId:     e.Connection.PartyID,
		},
		Environment: e.Environment,
		EvseUID:     e.EvseUID,
		Site:        e.Site,
		UccID:       e.UccID,
	}
}

func (e *EvseData) ToBson(value *openapi.EvseData) *EvseData {
	return &EvseData{
		ChargerID: value.ChargerID,
		Connection: Connection{
			CountryCode: value.Connection.CountryCode,
			PartyID:     value.Connection.PartyId,
		},
		Environment: value.Environment,
		EvseUID:     value.EvseUID,
		Site:        value.Site,
		UccID:       value.UccID,
	}
}
