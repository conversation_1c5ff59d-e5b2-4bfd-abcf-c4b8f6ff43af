package mongodb

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"inv-cloud-platform/fe-ev-opt-evse-api/config"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/port"
	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

var _ port.EvseRepository = (*EvseRepo)(nil)

type EvseRepo struct {
	client *mongo.Client
	cfg    *config.Configs
}

// GetEvsesByDeviceID implements port.EvseRepository.
func (e *EvseRepo) GetEvsesByDeviceID(ctx context.Context, deviceID string) ([]openapi.EvseData, error) {
	result := []EvseData{}
	filter := bson.D{
		{Key: "ucc_id", Value: deviceID},
	}
	cursor, err := e.client.Database(e.cfg.MongoDB.Database).Collection(e.cfg.MongoDB.Collection).Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	if errAll := cursor.All(ctx, &result); errAll != nil {
		return nil, errAll
	}
	evses := []openapi.EvseData{}
	for _, v := range result {
		evses = append(evses, *v.ToJSON())
	}
	return evses, nil
}

// Get implements port.EvseService.
func (e *EvseRepo) Get(ctx context.Context, evseID string) (*openapi.EvseData, error) {
	filter := bson.D{
		{Key: "evse_uid", Value: bson.D{{Key: "$regex", Value: evseID}, {Key: "$options", Value: "i"}}},
	}
	result := &EvseData{}
	if err := e.client.Database(e.cfg.MongoDB.Database).Collection(e.cfg.MongoDB.Collection).FindOne(ctx, filter).Decode(result); err != nil {
		return nil, err
	}
	return result.ToJSON(), nil
}

// Upsert implements port.EvseRepository.
func (e *EvseRepo) Upsert(ctx context.Context, input *openapi.EvseData) error {
	value := &EvseData{}
	value = value.ToBson(input)
	filter := bson.D{
		{Key: "evse_uid", Value: bson.D{{Key: "$regex", Value: input.EvseUID}, {Key: "$options", Value: "i"}}},
	}
	opts := options.Update().SetUpsert(true)
	update := bson.M{
		"$set": value,
	}
	_, err := e.client.Database(e.cfg.MongoDB.Database).Collection(e.cfg.MongoDB.Collection).UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return err
	}
	return nil
}

func NewEvseRepo(cfg *config.Configs, client *mongo.Client) *EvseRepo {
	return &EvseRepo{cfg: cfg, client: client}
}
