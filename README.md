#  FE-EV-OPT-EVSE-API

This services is used to store and get information about evse.

## First steps
-  Make sure to run `go mod tidy` to ensure all your imports are satisfied
- Run `go get -u ./...` to update to the most recent packages
- Notice that in the template we have `TODO` comments in multiple files, search for them, and make the necessary changes to match your app criteria

### Tooling
* golang v1.23
* docker 
* `make` - this project uses Makefiles

### Setup
To set up golang dependencies: `go mod download` and then `go mod tidy`

### Static Analysis Tools
The project uses [golangci-lin](https://golangci-lint.run/usage/quick-start/) lint tool to detect potential issues, bugs, and code style violations in Go codebases.

`make lint`

### Scripts
Many convenience commands are defined in the `Makefile`, run `make help` to learn more.

### Unit Tests
`make test`

### Docker Image
To create and push docker image to the image repo, one needs to create and push tag following the semantic versioning `release/v.a.b.c`
```
  git tag release/v0.0.1 && git tag push origin release/v0.0.1
```

### OpenAPI Generator docs
The app uses OpenAPI Generator to generate API docs. To generate docs, run:
`make codegen`

### Deployment
To deploy the app, one needs to use https://github.com/inv-cloud-platform/{subsystem}-deploy by following the steps listed in README.md.

## API

You can access and interact with this API by accessing one of the available endpoints:

- [`/swagger`](http://localhost:8080/swagger)
- [`/scalar`](http://localhost:8080/scalar)
- [`/redoc`](http://localhost:8080/redoc)