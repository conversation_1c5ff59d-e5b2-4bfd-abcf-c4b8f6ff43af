package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/inv-cloud-platform/hub-com-tools-go/hubmiddlewares"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"
	"github.com/rs/zerolog/log"
	"inv-cloud-platform/fe-ev-opt-evse-api/config"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/adapter/mongodb"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/common"
	"inv-cloud-platform/fe-ev-opt-evse-api/internal/core/service"
	"inv-cloud-platform/fe-ev-opt-evse-api/openapi"
)

func main() {
	log.Info().Msg("Application starting ...")
	cfg := config.New()
	common.NewLogger(cfg.App.LogLevel)

	appCtx := context.Background()
	mongoClient, errMongo := hubmongo.Connect(appCtx)
	if errMongo != nil {
		log.Fatal().Err(errMongo).Msg("unable to connect to mongodb")
	}
	evseRepo := mongodb.NewEvseRepo(cfg, mongoClient)
	evseSrv := service.NewEvseService(evseRepo)
	evseController := resthttp.NewEvseController(evseSrv)
	evseHandler := openapi.NewStrictHandler(evseController, nil)

	oapiSpec, errSw := openapi.GetSwagger()
	if errSw != nil {
		log.Warn().Err(errSw).Msg("unable to fetch openapi spec")
	}

	httpServer := resthttp.NewHTTPServer(
		cfg.App,
		openapi.Handler(evseHandler),
		resthttp.MetricsCollector,
		hubmiddlewares.RequestId,
		hubmiddlewares.Spec(
			hubmiddlewares.SpecOptionsTitle(oapiSpec.Info.Title),
			hubmiddlewares.SpecOptionsObjectSpec(oapiSpec),
		),
	)
	go httpServer.Start()
	defer func(ctx context.Context) {
		errS := httpServer.Shutdown(ctx)
		if errS != nil {
			log.Err(errS).Msg("HTTP server shutdown error")
		}
	}(appCtx)

	ctx, stop := signal.NotifyContext(appCtx, os.Interrupt, os.Kill, syscall.SIGTERM, syscall.SIGINT)
	defer stop()
	log.Info().Msg("application is running")

	<-ctx.Done()
	log.Info().Msg("Application stopping ...")
}
