// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
)

// Connection defines model for Connection.
type Connection struct {
	CountryCode string `json:"country_code,omitempty"`
	PartyId     string `json:"party_id,omitempty"`
}

// CreateEvseResponse defines model for CreateEvseResponse.
type CreateEvseResponse = EvseData

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// EvseData defines model for EvseData.
type EvseData struct {
	ChargerID   string     `json:"charger_id"`
	Connection  Connection `json:"connection"`
	Environment string     `json:"environment"`
	EvseUID     string     `json:"evse_uid"`
	Site        string     `json:"site"`
	UccID       string     `json:"ucc_id"`
}

// Evses defines model for Evses.
type Evses struct {
	Data   []EvseData `json:"data,omitempty"`
	Status bool       `json:"status,omitempty"`
}

// DeviceID defines model for DeviceID.
type DeviceID = string

// EvseUID defines model for EvseUID.
type EvseUID = string

// N204 defines model for 204.
type N204 = ErrorResponse

// N400 defines model for 400.
type N400 = ErrorResponse

// GetEvseParams defines parameters for GetEvse.
type GetEvseParams struct {
	// EvseUid evse id
	EvseUid *EvseUID `form:"evse_uid,omitempty" json:"evse_uid,omitempty"`
}

// CreateEvseJSONRequestBody defines body for CreateEvse for application/json ContentType.
type CreateEvseJSONRequestBody = EvseData

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /device/evse)
	GetEvse(w http.ResponseWriter, r *http.Request, params GetEvseParams)

	// (POST /device/evse)
	CreateEvse(w http.ResponseWriter, r *http.Request)

	// (GET /device/evses/{device_id})
	GetEvses(w http.ResponseWriter, r *http.Request, deviceID DeviceID)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// (GET /device/evse)
func (_ Unimplemented) GetEvse(w http.ResponseWriter, r *http.Request, params GetEvseParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (POST /device/evse)
func (_ Unimplemented) CreateEvse(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /device/evses/{device_id})
func (_ Unimplemented) GetEvses(w http.ResponseWriter, r *http.Request, deviceID DeviceID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetEvse operation middleware
func (siw *ServerInterfaceWrapper) GetEvse(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetEvseParams

	// ------------- Optional query parameter "evse_uid" -------------

	err = runtime.BindQueryParameter("form", true, false, "evse_uid", r.URL.Query(), &params.EvseUid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "evse_uid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEvse(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateEvse operation middleware
func (siw *ServerInterfaceWrapper) CreateEvse(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateEvse(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetEvses operation middleware
func (siw *ServerInterfaceWrapper) GetEvses(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "device_id" -------------
	var deviceID DeviceID

	err = runtime.BindStyledParameterWithOptions("simple", "device_id", chi.URLParam(r, "device_id"), &deviceID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "device_id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEvses(w, r, deviceID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/device/evse", wrapper.GetEvse)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/device/evse", wrapper.CreateEvse)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/device/evses/{device_id}", wrapper.GetEvses)
	})

	return r
}

type N204JSONResponse ErrorResponse

type N400JSONResponse ErrorResponse

type GetEvseRequestObject struct {
	Params GetEvseParams
}

type GetEvseResponseObject interface {
	VisitGetEvseResponse(w http.ResponseWriter) error
}

type GetEvse200JSONResponse EvseData

func (response GetEvse200JSONResponse) VisitGetEvseResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetEvse204JSONResponse struct{ N204JSONResponse }

func (response GetEvse204JSONResponse) VisitGetEvseResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(204)

	return json.NewEncoder(w).Encode(response)
}

type GetEvse400JSONResponse struct{ N400JSONResponse }

func (response GetEvse400JSONResponse) VisitGetEvseResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type CreateEvseRequestObject struct {
	Body *CreateEvseJSONRequestBody
}

type CreateEvseResponseObject interface {
	VisitCreateEvseResponse(w http.ResponseWriter) error
}

type CreateEvse200JSONResponse CreateEvseResponse

func (response CreateEvse200JSONResponse) VisitCreateEvseResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type CreateEvse400JSONResponse struct{ N400JSONResponse }

func (response CreateEvse400JSONResponse) VisitCreateEvseResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type GetEvsesRequestObject struct {
	DeviceID DeviceID `json:"device_id"`
}

type GetEvsesResponseObject interface {
	VisitGetEvsesResponse(w http.ResponseWriter) error
}

type GetEvses200JSONResponse Evses

func (response GetEvses200JSONResponse) VisitGetEvsesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetEvses204JSONResponse struct{ N204JSONResponse }

func (response GetEvses204JSONResponse) VisitGetEvsesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(204)

	return json.NewEncoder(w).Encode(response)
}

type GetEvses400JSONResponse struct{ N400JSONResponse }

func (response GetEvses400JSONResponse) VisitGetEvsesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {

	// (GET /device/evse)
	GetEvse(ctx context.Context, request GetEvseRequestObject) (GetEvseResponseObject, error)

	// (POST /device/evse)
	CreateEvse(ctx context.Context, request CreateEvseRequestObject) (CreateEvseResponseObject, error)

	// (GET /device/evses/{device_id})
	GetEvses(ctx context.Context, request GetEvsesRequestObject) (GetEvsesResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// GetEvse operation middleware
func (sh *strictHandler) GetEvse(w http.ResponseWriter, r *http.Request, params GetEvseParams) {
	var request GetEvseRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetEvse(ctx, request.(GetEvseRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetEvse")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetEvseResponseObject); ok {
		if err := validResponse.VisitGetEvseResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// CreateEvse operation middleware
func (sh *strictHandler) CreateEvse(w http.ResponseWriter, r *http.Request) {
	var request CreateEvseRequestObject

	var body CreateEvseJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.CreateEvse(ctx, request.(CreateEvseRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "CreateEvse")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(CreateEvseResponseObject); ok {
		if err := validResponse.VisitCreateEvseResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetEvses operation middleware
func (sh *strictHandler) GetEvses(w http.ResponseWriter, r *http.Request, deviceID DeviceID) {
	var request GetEvsesRequestObject

	request.DeviceID = deviceID

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetEvses(ctx, request.(GetEvsesRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetEvses")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetEvsesResponseObject); ok {
		if err := validResponse.VisitGetEvsesResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xWTW/bMAz9K4a2oxNnXU+5rW0w9DIMLbpLERSqzCbqYkkVZa9GoP8+UHb8AXvp17Lt",
	"sFNik+ajHp/0tGVCZ0YrUA7ZfMsMtzwDBzY8nUEhBZyf0f8UUFhpnNSKzVkaIpFMWcwkvTDcrVnMFM+g",
	"Cd+EsIWHXFpI2dzZHGKGYg0Zp5KuNJSMzkq1Yj5mj5OVntQlGmwfs0WBcDXWBhTYbeIhB1u2XVD0Jg/h",
	"X4J6Tx2i0QohrPlodkw/QisHytFfbsxGCk6QyT0S7rZT772FOzZn75KWyKSKYrKwVtuLunqF1e//i452",
	"QD5mx7PZn4M+4Wl0AQ85oGMUrT+kuqdaKRBV3pYZqw1YJyt6hM6Vs+WN0CkM6axHSC8n+F2aiQ5wfDMx",
	"WioHthKBj0loriSBvLoGNX1qgTsgeTRLHXa85nYFdh9WrZfTKrPSnOiRsI/qDl0+ZqAKabXK6gkONN6I",
	"8olmdpqn0UgHo7VyIZ6xrCshqI7vbsXr7uaoy9RA/SXEXf56rCzjHaq+vQcRFNxX3Yh43iaaDBD5KpSA",
	"R56ZDVW5qBcV3UnYpJHEKJOIQN2+Ulh9okLTLfbosguEM+74f/H9VfEV9RHeH0FaD0Y6yPDJY3M3Sd8A",
	"cGt5+QKRouMuxw4vt1pvgKsXHGxEl1R3eucGXNA4B0f44tvlIroEbsU6+vT1nAQvXdgTw0gBFquvPkxn",
	"0xmtTxtQ3Eg2Zx+ns+kRi4OJh86TysATmhM9r8ANzfczuIirKHjwbVnZMBEf7Oo8rTKI0FC5vVdcj4+g",
	"TUka+S0H5vwbHbIZ9dAcL3MhAJFYqu8DY5Wa1hJKag18fy4lEaLjK9ztBbYkS9Q4wnJlcRGPFPwIXA9Y",
	"bk2wvm0BuhOdlgdhyh9wIiNuvn82b+Hbxz2VY7JtLq3+OZJH0nz3GjwqfXyx9tt776HFj/+I8juvtmMn",
	"HIINLNNZ1dztKcL80v8MAAD//8gqXHzBDAAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
