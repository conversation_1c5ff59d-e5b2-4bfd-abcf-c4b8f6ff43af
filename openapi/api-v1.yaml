openapi: 3.0.2
info:
  version: 1.0.0
  title: EVSE Search API
  description: EVSE Search API
  contact: {}
tags:
  - name: EVSE
    description: EVSE service api

paths:
  /device/evse:
    get:
      tags:
        - evse
      operationId: getEvse
      description: Get an evse by id
      parameters:
        - $ref: '#/components/parameters/EvseUID'
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EvseData"
        "400":
          $ref: "#/components/responses/400"
        "204":
          $ref: "#/components/responses/204"
    post:
      tags:
        - evse
      operationId: createEvse
      description: Create a new evse
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EvseData"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateEvseResponse"
        "400":
          $ref: "#/components/responses/400"
  /device/evses/{device_id}:
    get:
      tags:
        - evse
      operationId: getEvses
      description: Get an evses by device id
      parameters:
        - $ref: '#/components/parameters/DeviceID'
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Evses"
        "400":
          $ref: "#/components/responses/400"
        "204":
          $ref: "#/components/responses/204"
  /device/evses:
    get:
      tags:
        - evse
      operationId: getEvses
      description: Get all evses
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'

      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Evses"
        "400":
          $ref: "#/components/responses/400"
        "204":
          $ref: "#/components/responses/204"
components:
  parameters:
    EvseUID:
      name: evse_uid
      in: query
      schema:
        type: string
      description: evse id
    DeviceID:
      name: device_id
      in: path
      required: true
      x-go-name: DeviceID
      schema:
        type: string
      description: device id
  schemas:
    Connection:
      properties:
        party_id:
          type: string
          x-go-type-skip-optional-pointer: true
        country_code: 
          type: string
          x-go-type-skip-optional-pointer: true
    Evses:
      properties:
        status:
          type: boolean
          x-go-type-skip-optional-pointer: true    
        data:
          type: array
          items:
            $ref: '#/components/schemas/EvseData'
          x-go-type-skip-optional-pointer: true    
    EvseData:
      properties:
        evse_uid:
          type: string
          x-go-name: EvseUID
        ucc_id:
          type: string
          x-go-name: UccID
        site:
          type: string
        environment:
          type: string
        connection:
          $ref: "#/components/schemas/Connection"
        charger_id:
          type: string
          x-go-name: ChargerID
      required:
        - evse_uid
        - ucc_id
        - site
        - environment
        - charger_id
        - connection
      type: object
      
    CreateEvseResponse:
      type: object
      $ref: "#/components/schemas/EvseData"
    SearchRequest:
      type: object
      properties:
        filter:
          description: Scim v2 filter syntax (RFC 7644 Section *******)
          type: string
          example: 'ucc.swProperties.uccVersion.dv eq \"1.2.3\"'
        offset:
          description: Page number
          type: integer
          format: int64
          example: 1
        limit:
          description: Limit per page
          type: integer
          format: int64
          minimum: 10
          maximum: 100
          example: 50
        sort:
          description: Sort search by fields
          x-go-type-skip-optional-pointer: true
          type: object
          additionalProperties:
            type: integer
            minimum: -1
            maximum: 1
          example:
            site.name: -1
        projection:
          description: Fields to include or exclude. The value 1 indicates inclusion of the field, and 0 indicates exclusion.
          x-go-type-skip-optional-pointer: true
          type: object
          additionalProperties:
            type: integer
            minimum: 0
            maximum: 1
          example:
            site.id: 1
    ErrorResponse:
      type: object
      properties:
        code:
          type: string
          x-go-type-skip-optional-pointer: true
        message:
          type: string
          example: Required field is missed
          x-go-type-skip-optional-pointer: true
      required:
        - code
        - message
  responses:
    200:
      description: Success
    400:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    204:
      description: No content
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    500:
      description: Internal Server Error
      headers:
        x-hub-request-id:
          description: Unique identifier for the request
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'